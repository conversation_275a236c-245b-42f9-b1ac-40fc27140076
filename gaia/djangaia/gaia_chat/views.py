from django.shortcuts import render, redirect
from django.http import HttpResponse, JsonResponse
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.contrib.auth.forms import AuthenticationForm, PasswordResetForm, SetPasswordForm, PasswordChangeForm
from django.contrib.auth.models import User
from django.contrib import messages
from django.urls import reverse_lazy
from django.views.generic.edit import FormView
from django.contrib.auth.views import PasswordResetView, PasswordResetDoneView, PasswordResetConfirmView, PasswordResetCompleteView
from django.core.mail import send_mail
from django.conf import settings
import os
import json

from .forms import UserRegistrationForm, UserProfileForm


@login_required
def chat_app(request):
    """
    Renders the Vue.js chat application.
    Requires user to be authenticated.
    """
    # Check if API keys are available
    openai_api_key = os.environ.get("OPENAI_API_KEY", "")
    anthropic_api_key = os.environ.get("ANTHROPIC_API_KEY", "")

    context = {
        'title': 'Gaia Chat',
        'has_openai_key': bool(openai_api_key),
        'has_anthropic_key': bool(anthropic_api_key),
        'user': request.user,
        # No chat_context by default
    }
    return render(request, 'gaia_chat/chat_app.html', context)


def login_view(request):
    """
    Handle user login.
    """
    if request.user.is_authenticated:
        return redirect('gaia_chat:chat_app')

    if request.method == 'POST':
        form = AuthenticationForm(request, data=request.POST)
        if form.is_valid():
            username = form.cleaned_data.get('username')
            password = form.cleaned_data.get('password')
            user = authenticate(username=username, password=password)
            if user is not None:
                login(request, user)
                messages.success(request, f"Welcome back, {username}!")
                return redirect('gaia_chat:chat_app')
            else:
                messages.error(request, "Invalid username or password.")
        else:
            messages.error(request, "Invalid username or password.")
    else:
        form = AuthenticationForm()

    return render(request, 'gaia_chat/auth/login.html', {'form': form})


def register_view(request):
    """
    Handle user registration.
    """
    if request.user.is_authenticated:
        return redirect('gaia_chat:chat_app')

    if request.method == 'POST':
        form = UserRegistrationForm(request.POST)
        if form.is_valid():
            user = form.save()
            login(request, user)
            messages.success(request, f"Account created for {user.username}!")
            return redirect('gaia_chat:chat_app')
    else:
        form = UserRegistrationForm()

    return render(request, 'gaia_chat/auth/register.html', {'form': form})


def logout_view(request):
    """
    Handle user logout.
    """
    logout(request)
    messages.info(request, "You have been logged out.")
    return redirect('gaia_chat:login')


@login_required
def profile_view(request):
    """
    User profile view for updating personal information.
    """
    if request.method == 'POST':
        form = UserProfileForm(request.POST, instance=request.user)
        if form.is_valid():
            form.save()
            messages.success(request, "Your profile has been updated!")
            return redirect('gaia_chat:profile')
    else:
        form = UserProfileForm(instance=request.user)

    return render(request, 'gaia_chat/auth/profile.html', {'form': form})


@login_required
def change_password_view(request):
    """
    Allow users to change their password.
    """
    if request.method == 'POST':
        form = PasswordChangeForm(request.user, request.POST)
        if form.is_valid():
            user = form.save()
            messages.success(request, "Your password has been changed successfully!")
            return redirect('gaia_chat:profile')
    else:
        form = PasswordChangeForm(request.user)

    return render(request, 'gaia_chat/auth/change_password.html', {'form': form})


@login_required
def companies_chat_view(request):
    """
    Renders the Vue.js chat application with a context sidebar for companies.
    Requires user to be authenticated.
    """
    # Check if API keys are available
    openai_api_key = os.environ.get("OPENAI_API_KEY", "")
    anthropic_api_key = os.environ.get("ANTHROPIC_API_KEY", "")
    
    # Sample company data
    context_data = {
        "type": "companies",
        "title": "Company Information",
        "description": "Information about various companies that can be used as context in your chat.",
        "data": [
            {
                "id": 1,
                "name": "Acme Corporation",
                "industry": "Manufacturing",
                "founded": 1942,
                "employees": 5000,
                "revenue": "$500M",
                "description": "A global leader in manufacturing innovative products."
            },
            {
                "id": 2,
                "name": "TechNova",
                "industry": "Technology",
                "founded": 2010,
                "employees": 250,
                "revenue": "$75M",
                "description": "Cutting-edge software solutions for enterprise clients."
            },
            {
                "id": 3,
                "name": "GreenEarth",
                "industry": "Renewable Energy",
                "founded": 2015,
                "employees": 120,
                "revenue": "$30M",
                "description": "Sustainable energy solutions for a better future."
            }
        ]
    }
    
    context = {
        'title': 'Gaia Chat with Company Context',
        'has_openai_key': bool(openai_api_key),
        'has_anthropic_key': bool(anthropic_api_key),
        'user': request.user,
        'chat_context': json.dumps(context_data)
    }
    
    return render(request, 'gaia_chat/chat_app.html', context)


@login_required
@require_http_methods(["GET"])
def get_company_data(request):
    """API endpoint to get company data"""
    company_data = [
        {
            "id": 1,
            "name": "Acme Corporation",
            "industry": "Manufacturing",
            "founded": 1942,
            "employees": 5000,
            "revenue": "$500M",
            "description": "A global leader in manufacturing innovative products."
        },
        {
            "id": 2,
            "name": "TechNova",
            "industry": "Technology",
            "founded": 2010,
            "employees": 250,
            "revenue": "$75M",
            "description": "Cutting-edge software solutions for enterprise clients."
        },
        {
            "id": 3,
            "name": "GreenEarth",
            "industry": "Renewable Energy",
            "founded": 2015,
            "employees": 120,
            "revenue": "$30M",
            "description": "Sustainable energy solutions for a better future."
        }
    ]
    return JsonResponse({"companies": company_data})


# Update the chat view to handle context properly
@csrf_exempt
def chat_api(request):
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            conversation_id = data.get('conversation_id')
            messages = data.get('messages', [])
            provider = data.get('provider', 'openai')
            
            # Ensure we have the necessary data
            if not conversation_id or not messages:
                return JsonResponse({'error': 'Missing required parameters'}, status=400)
            
            # Get the conversation
            try:
                conversation = Conversation.objects.get(id=conversation_id)
            except Conversation.DoesNotExist:
                return JsonResponse({'error': 'Conversation not found'}, status=404)
            
            # Process system messages to enhance context handling
            processed_messages = []
            for msg in messages:
                if msg['role'] == 'system' and 'CONTEXT ITEM' in msg.get('content', ''):
                    # Make context items more prominent
                    msg['content'] = f"IMPORTANT CONTEXT: {msg['content']}"
                processed_messages.append(msg)
            
            # Get the response from the LLM
            response = get_llm_response(processed_messages, provider)
            
            return JsonResponse({'message': response})
        except Exception as e:
            return JsonResponse({'error': str(e)}, status=500)
    else:
        return JsonResponse({'error': 'Method not allowed'}, status=405)


# Simplify the conversation update endpoint
@csrf_exempt
def conversation_update_api(request, conversation_id):
    """
    API endpoint to update a conversation.
    """
    print('FOOO ////////////////////////////////////////////////////////')
    if request.method == 'POST':
        try:
            # Log the incoming request for debugging
            logger.info(f"Updating conversation with ID: {conversation_id}")
            
            # Parse the request body
            data = json.loads(request.body)
            messages = data.get('messages', [])
            
            # Get the conversation
            try:
                # Try to get the conversation directly with the string ID
                conversation = Conversation.objects.get(id=conversation_id)
                logger.info(f"Found conversation: {conversation.title}")
            except Conversation.DoesNotExist:
                # If that fails, try to convert to UUID
                try:
                    import uuid
                    uuid_id = uuid.UUID(conversation_id)
                    conversation = Conversation.objects.get(id=uuid_id)
                    logger.info(f"Found conversation with UUID: {conversation.title}")
                except (ValueError, Conversation.DoesNotExist):
                    logger.error(f"Conversation with ID {conversation_id} not found")
                    return JsonResponse({
                        'success': False,
                        'error': f'Conversation with ID {conversation_id} not found'
                    }, status=404)
            
            # Update the conversation messages
            conversation.messages = messages
            conversation.updated_at = timezone.now()
            conversation.save()
            
            logger.info(f"Successfully updated conversation: {conversation_id}")
            
            return JsonResponse({
                'success': True,
                'conversation': {
                    'id': str(conversation.id),
                    'title': conversation.title,
                    'messages': conversation.messages,
                    'created_at': conversation.created_at.isoformat(),
                    'updated_at': conversation.updated_at.isoformat()
                }
            })
        except json.JSONDecodeError:
            logger.error("Invalid JSON in request body")
            return JsonResponse({
                'success': False,
                'error': 'Invalid JSON in request body'
            }, status=400)
        except Exception as e:
            logger.error(f"Error updating conversation: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            return JsonResponse({
                'success': False,
                'error': f'Error updating conversation: {str(e)}'
            }, status=500)
    else:
        return JsonResponse({
            'success': False,
            'error': 'Method not allowed'
        }, status=405)










"""
API views for the gaia_chat app.

These views provide a REST API for interacting with the chatobj functionality.
All API views require authentication.
"""

import json
import os
import logging
import asyncio
from datetime import datetime
from django.http import JsonResponse, StreamingHttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from typing import Dict, Any, List, Optional

# Import chatobj components
from gaia.gaia_ceto.ceto_v002.chatobj import (
    ChatManager, Conversation, MockLLM, OpenAILLM, AnthropicLLM
)

# Import MCP client libraries if available
MCP_SSE_AVAILABLE = False
MCP_HTTP_AVAILABLE = False

# Try to import SSE client
try:
    from gaia.gaia_ceto.proto_mcp.mcp_sse_clientlib import MCPClientLib as MCPSSEClientLib
    MCP_SSE_AVAILABLE = True
except ImportError:
    logging.warning("MCP SSE client library not available. MCP SSE provider will not work.")

# Try to import HTTP client
try:
    from gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib import MCPClientLib as MCPHTTPClientLib
    MCP_HTTP_AVAILABLE = True
except ImportError:
    logging.warning("MCP HTTP client library not available. MCP HTTP provider will not work.")

# For backward compatibility
MCP_AVAILABLE = MCP_SSE_AVAILABLE


# Define MCPSSELLM class for web UI
class MCPSSELLM:
    """MCP SSE client implementation of the LLM interface for web UI."""

    def __init__(self, server_url: str = "http://0.0.0.0:9000/sse", model_name: str = "claude-3-5-sonnet-20240620"):
        """Initialize the MCP SSE LLM.

        Args:
            server_url: The URL of the MCP SSE server.
            model_name: The name of the Claude model to use.
        """
        if not MCP_SSE_AVAILABLE:
            raise ImportError("MCP SSE client library is not available. Cannot use MCP SSE LLM.")

        self.server_url = server_url
        self.model_name = model_name
        self.client = None
        self.messages = []
        self.loop = asyncio.new_event_loop()
        self.protocol = "SSE"  # Indicate which protocol is being used

        # Initialize the MCP client
        self.client = self._init_client()

    def _init_client(self):
        """Initialize the MCP SSE client."""
        client = MCPSSEClientLib(debug_callback=self._debug_callback)

        # Connect to the server
        logging.info(f"Connecting to MCP server via SSE at: {self.server_url}")
        success = self.loop.run_until_complete(client.connect_to_server(self.server_url))

        if not success:
            raise ConnectionError(f"Failed to connect to MCP server at {self.server_url}")

        # Log available tools
        tool_names = [tool['name'] for tool in client.available_tools]
        logging.info(f"Connected to server with tools: {', '.join(tool_names)}")

        return client

    def _debug_callback(self, level: str, message: str, data: Any = None):
        """Callback for debug messages from the client library."""
        if level == "error":
            logging.error(message)
        elif level == "warning":
            logging.warning(message)
        elif level == "info":
            logging.info(message)
        elif level == "debug":
            logging.debug(message)

    def generate_response(self, prompt: str, context: List[Dict[str, Any]], **kwargs) -> str:
        """Generate a response using the MCP SSE client.

        Args:
            prompt: The user's input text.
            context: The conversation history.
            **kwargs: Additional parameters to pass to the MCP client.

        Returns:
            The generated response.
        """
        try:
            # Note: The quote handling for direct tool calls is handled in chatobj.py

            # Convert context to the format expected by the MCP client
            # Extract system messages separately for the MCP client
            messages = []
            system_messages = []

            # Extract user and assistant messages for the messages array
            # and system messages separately
            for msg in context:
                if msg["role"] in ["user", "assistant"]:
                    messages.append({
                        "role": msg["role"],
                        "content": msg["content"]
                    })
                elif msg["role"] == "system":
                    system_messages.append(msg["content"])

            # Process the query
            result = self.loop.run_until_complete(self.client.process_query(
                query=prompt,
                model=self.model_name,
                max_tokens=kwargs.get("max_tokens", 1024),
                tool_timeout=kwargs.get("tool_timeout", 3600),  #  for long-running tools
                conversation_history=messages,
                system_messages=system_messages
            ))

            # Update conversation history
            self.messages = result["messages"]

            # Print any errors
            if result["error"]:
                logging.error(f"Error from MCP SSE client: {result['error']}")
                return f"Error: {result['error']}"

            # Format tool results if any
            tool_results_text = ""
            if result["tool_results"]:
                tool_calls_info = f"\nClaude requested {len(result['tool_results'])} tool call(s).\n"
                for i, tool_result in enumerate(result["tool_results"]):
                    tool_calls_info += f"\nTool Call {i+1}: '{tool_result.tool_name}'\n"
                    if tool_result.success:
                        tool_calls_info += f"Result: {tool_result.content}\n"
                    else:
                        tool_calls_info += f"Error: {tool_result.error}\n"

                tool_results_text = tool_calls_info

            # Return Claude's response with tool results
            if tool_results_text:
                return f"{tool_results_text}\n\n{result['final_text']}"
            else:
                return result["final_text"]

        except Exception as e:
            logging.error(f"Error generating response from MCP SSE: {e}")
            return f"Error generating response: {str(e)}"

    def cleanup(self):
        """Explicitly clean up resources. Should be called before object destruction."""
        if self.client:
            try:
                self.loop.run_until_complete(self.client.cleanup())
                logging.info("MCP SSE client cleaned up successfully")
            except Exception as e:
                logging.error(f"Error cleaning up MCP SSE client: {e}")
            finally:
                self.client = None

        # Close the event loop
        if self.loop and not self.loop.is_closed():
            try:
                self.loop.close()
                logging.info("Event loop closed successfully")
            except Exception as e:
                logging.error(f"Error closing event loop: {e}")

    def __del__(self):
        """Clean up resources when the object is destroyed."""
        # Only attempt cleanup if we still have resources and the loop is running
        if self.client and self.loop and not self.loop.is_closed():
            try:
                # Check if the loop is running in the current thread
                if self.loop.is_running():
                    # If the loop is running, we can't use run_until_complete
                    # Just log a warning and let the resources be cleaned up by the GC
                    logging.warning("MCP SSE client cleanup skipped - event loop is running")
                else:
                    # Safe to run cleanup
                    self.loop.run_until_complete(self.client.cleanup())
                    logging.info("MCP SSE client cleaned up in destructor")
            except Exception as e:
                logging.warning(f"Error cleaning up MCP SSE client in destructor: {e}")

        # Close the event loop if it's not already closed
        if hasattr(self, 'loop') and self.loop and not self.loop.is_closed():
            try:
                self.loop.close()
            except Exception as e:
                logging.warning(f"Error closing event loop in destructor: {e}")

    @classmethod
    def get_available_models(cls):
        """Get available models for MCP SSE.

        Returns:
            A list of available model names.
        """
        return ["claude-3-5-sonnet-20240620", "claude-3-opus-20240229", "claude-3-haiku-20240307"]


# Define MCPHTTPLLM class for web UI
class MCPHTTPLLM:
    """MCP HTTP client implementation of the LLM interface for web UI."""

    def __init__(self, server_url: str = "http://0.0.0.0:9000/mcp", model_name: str = "claude-3-5-sonnet-20240620"):
        """Initialize the MCP HTTP LLM.

        Args:
            server_url: The URL of the MCP HTTP server.
            model_name: The name of the Claude model to use.
        """
        if not MCP_HTTP_AVAILABLE:
            raise ImportError("MCP HTTP client library is not available. Cannot use MCP HTTP LLM.")

        self.server_url = server_url
        self.model_name = model_name
        self.client = None
        self.messages = []
        self.loop = asyncio.new_event_loop()
        self.protocol = "HTTP"  # Indicate which protocol is being used

        # Initialize the MCP client
        self.client = self._init_client()

    def _init_client(self):
        """Initialize the MCP HTTP client."""
        client = MCPHTTPClientLib(debug_callback=self._debug_callback)

        # Connect to the server
        logging.info(f"Connecting to MCP server via HTTP at: {self.server_url}")
        success = self.loop.run_until_complete(client.connect_to_server(self.server_url))

        if not success:
            raise ConnectionError(f"Failed to connect to MCP server at {self.server_url}")

        # Log available tools
        tool_names = [tool['name'] for tool in client.available_tools]
        logging.info(f"Connected to server with tools: {', '.join(tool_names)}")

        return client

    def _debug_callback(self, level: str, message: str, data: Any = None):
        """Callback for debug messages from the client library."""
        if level == "error":
            logging.error(message)
        elif level == "warning":
            logging.warning(message)
        elif level == "info":
            logging.info(message)
        elif level == "debug":
            logging.debug(message)

    def generate_response(self, prompt: str, context: List[Dict[str, Any]], **kwargs) -> str:
        """Generate a response using the MCP HTTP client.

        Args:
            prompt: The user's input text.
            context: The conversation history.
            **kwargs: Additional parameters to pass to the MCP client.

        Returns:
            The generated response.
        """
        try:
            # Note: The quote handling for direct tool calls is handled in chatobj.py

            # Convert context to the format expected by the MCP client
            # Extract system messages separately for the MCP client
            messages = []
            system_messages = []

            # Extract user and assistant messages for the messages array
            # and system messages separately
            for msg in context:
                if msg["role"] in ["user", "assistant"]:
                    messages.append({
                        "role": msg["role"],
                        "content": msg["content"]
                    })
                elif msg["role"] == "system":
                    system_messages.append(msg["content"])

            # Process the query
            result = self.loop.run_until_complete(self.client.process_query(
                query=prompt,
                model=self.model_name,
                max_tokens=kwargs.get("max_tokens", 1024),
                tool_timeout=kwargs.get("tool_timeout", 3600),  # for long-running tools
                conversation_history=messages,
                system_messages=system_messages
            ))

            # Update conversation history
            self.messages = result["messages"]

            # Print any errors
            if result["error"]:
                logging.error(f"Error from MCP HTTP client: {result['error']}")
                return f"Error: {result['error']}"

            # Format tool results if any
            tool_results_text = ""
            if result["tool_results"]:
                tool_calls_info = f"\nClaude requested {len(result['tool_results'])} tool call(s).\n"
                for i, tool_result in enumerate(result["tool_results"]):
                    tool_calls_info += f"\nTool Call {i+1}: '{tool_result.tool_name}'\n"
                    if tool_result.success:
                        tool_calls_info += f"Result: {tool_result.content}\n"
                    else:
                        tool_calls_info += f"Error: {tool_result.error}\n"

                tool_results_text = tool_calls_info

            # Return Claude's response with tool results
            if tool_results_text:
                return f"{tool_results_text}\n\n{result['final_text']}"
            else:
                return result["final_text"]

        except Exception as e:
            logging.error(f"Error generating response from MCP HTTP: {e}")
            return f"Error generating response: {str(e)}"

    def cleanup(self):
        """Explicitly clean up resources. Should be called before object destruction."""
        if self.client:
            try:
                self.loop.run_until_complete(self.client.cleanup())
                logging.info("MCP HTTP client cleaned up successfully")
            except Exception as e:
                logging.error(f"Error cleaning up MCP HTTP client: {e}")
            finally:
                self.client = None

        # Close the event loop
        if self.loop and not self.loop.is_closed():
            try:
                self.loop.close()
                logging.info("Event loop closed successfully")
            except Exception as e:
                logging.error(f"Error closing event loop: {e}")

    def __del__(self):
        """Clean up resources when the object is destroyed."""
        # Only attempt cleanup if we still have resources and the loop is running
        if self.client and self.loop and not self.loop.is_closed():
            try:
                # Check if the loop is running in the current thread
                if self.loop.is_running():
                    # If the loop is running, we can't use run_until_complete
                    # Just log a warning and let the resources be cleaned up by the GC
                    logging.warning("MCP HTTP client cleanup skipped - event loop is running")
                else:
                    # Safe to run cleanup
                    self.loop.run_until_complete(self.client.cleanup())
                    logging.info("MCP HTTP client cleaned up in destructor")
            except Exception as e:
                logging.warning(f"Error cleaning up MCP HTTP client in destructor: {e}")

        # Close the event loop if it's not already closed
        if hasattr(self, 'loop') and self.loop and not self.loop.is_closed():
            try:
                self.loop.close()
            except Exception as e:
                logging.warning(f"Error closing event loop in destructor: {e}")

    @classmethod
    def get_available_models(cls):
        """Get available models for MCP HTTP.

        Returns:
            A list of available model names.
        """
        return ["claude-3-5-sonnet-20240620", "claude-3-opus-20240229", "claude-3-haiku-20240307"]


# For backward compatibility
MCPLLM = MCPSSELLM

# Set up logging
logger = logging.getLogger(__name__)

# Global chat manager instance
STORAGE_DIR = "/var/lib/gaia/GAIA_FS/ceto_conversations"
os.makedirs(STORAGE_DIR, exist_ok=True)
chat_manager = ChatManager(storage_dir=STORAGE_DIR)

# LLM factory function
def create_llm(llm_type="mock", model_name=None):
    """Create an LLM instance based on the specified type.

    Args:
        llm_type: The type of LLM to create ("mock", "openai", "anthropic", "mcp", or "mcp-http").
        model_name: The specific model name to use (if applicable).
                   For MCP providers, this can be a server URL.

    Returns:
        An instance of the specified LLM.
    """
    kwargs = {}
    if model_name:
        kwargs["model_name"] = model_name

    if llm_type.lower() == "mock":
        return MockLLM()
    elif llm_type.lower() == "openai":
        try:
            return OpenAILLM(**kwargs)
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI LLM: {e}")
            return MockLLM()
    elif llm_type.lower() == "anthropic":
        try:
            return AnthropicLLM(**kwargs)
        except Exception as e:
            logger.error(f"Failed to initialize Anthropic LLM: {e}")
            return MockLLM()
    elif llm_type.lower() == "mcp":
        if not MCP_SSE_AVAILABLE:
            logger.error("MCP SSE client library is not available. Cannot use MCP SSE LLM.")
            return MockLLM()

        try:
            # Default server URL if not specified in model_name
            server_url = "http://0.0.0.0:9000/sse"

            # If model_name contains a URL, use it as the server URL
            if model_name and "://" in model_name:
                server_url = model_name
                # Reset model_name to None to use the default model
                kwargs = {}

            return MCPSSELLM(server_url=server_url, **kwargs)
        except Exception as e:
            logger.error(f"Failed to initialize MCP SSE LLM: {e}")
            return MockLLM()
    elif llm_type.lower() == "mcp-http":
        if not MCP_HTTP_AVAILABLE:
            logger.error("MCP HTTP client library is not available. Cannot use MCP HTTP LLM.")
            return MockLLM()

        try:
            # Default server URL if not specified in model_name
            server_url = "http://0.0.0.0:9000/mcp"

            # If model_name contains a URL, use it as the server URL
            if model_name and "://" in model_name:
                server_url = model_name
                # Reset model_name to None to use the default model
                kwargs = {}

            return MCPHTTPLLM(server_url=server_url, **kwargs)
        except Exception as e:
            logger.error(f"Failed to initialize MCP HTTP LLM: {e}")
            return MockLLM()
    else:
        logger.warning(f"Unsupported LLM type: {llm_type}. Using MockLLM instead.")
        return MockLLM()

# API endpoints
@login_required
@require_http_methods(["GET"])
def get_llm_providers(request):
    """Get available LLM providers."""
    providers = [
        {"id": "mock", "name": "Mock LLM"},
        {"id": "openai", "name": "OpenAI"},
        {"id": "anthropic", "name": "Anthropic"}
    ]

    # Add MCP SSE provider if available
    if MCP_SSE_AVAILABLE:
        providers.append({"id": "mcp", "name": "MCP SSE (Tools)"})

    # Add MCP HTTP provider if available
    if MCP_HTTP_AVAILABLE:
        providers.append({"id": "mcp-http", "name": "MCP HTTP (Tools)"})

    return JsonResponse({"providers": providers})


@login_required
@require_http_methods(["GET"])
def get_company_data(request):
    """Get paginated company data for the companies context."""
    try:
        # Get pagination parameters
        page = int(request.GET.get('page', 1))
        per_page = int(request.GET.get('per_page', 10))
        search = request.GET.get('search', '').strip()

        # Load the companies data from the JSON file
        companies_file = os.path.join(
            os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
            'gaia_ceto', 'example_contexts', 'companies_example.json'
        )

        if not os.path.exists(companies_file):
            return JsonResponse({
                'error': 'Companies data file not found',
                'data': [],
                'pagination': {
                    'current_page': 1,
                    'total_pages': 1,
                    'total_companies': 0,
                    'per_page': per_page
                }
            }, status=404)

        with open(companies_file, 'r') as f:
            companies_data = json.load(f)

        all_companies = companies_data.get('data', [])

        # Apply search filter if provided
        if search:
            filtered_companies = []
            search_lower = search.lower()
            for company in all_companies:
                if (search_lower in company.get('name', '').lower() or
                    search_lower in company.get('industry', '').lower() or
                    search_lower in company.get('description', '').lower()):
                    filtered_companies.append(company)
            all_companies = filtered_companies

        # Calculate pagination
        total_companies = len(all_companies)
        total_pages = max(1, (total_companies + per_page - 1) // per_page)

        # Ensure page is within valid range
        page = max(1, min(page, total_pages))

        # Get the companies for this page
        start_idx = (page - 1) * per_page
        end_idx = start_idx + per_page
        page_companies = all_companies[start_idx:end_idx]

        return JsonResponse({
            'title': companies_data.get('title', 'Company Information'),
            'description': companies_data.get('description', 'Company data'),
            'data': page_companies,
            'pagination': {
                'current_page': page,
                'total_pages': total_pages,
                'total_companies': total_companies,
                'per_page': per_page
            }
        })

    except Exception as e:
        logger.error(f"Error getting company data: {e}")
        return JsonResponse({
            'error': str(e),
            'data': [],
            'pagination': {
                'current_page': 1,
                'total_pages': 1,
                'total_companies': 0,
                'per_page': per_page
            }
        }, status=500)

@login_required
@require_http_methods(["GET"])
def get_models(request):
    """Get available models for a specific LLM provider."""
    provider = request.GET.get("provider", "mock")

    if provider == "mock":
        models = [{"id": "default", "name": "Default Mock"}]
    elif provider == "openai":
        try:
            models = [{"id": model, "name": model} for model in OpenAILLM.get_available_models()]
        except Exception as e:
            logger.error(f"Error getting OpenAI models: {e}")
            models = [
                {"id": "gpt-3.5-turbo", "name": "GPT-3.5 Turbo"},
                {"id": "gpt-4", "name": "GPT-4"}
            ]
    elif provider == "anthropic":
        try:
            models = [{"id": model, "name": model} for model in AnthropicLLM.get_available_models()]
        except Exception as e:
            logger.error(f"Error getting Anthropic models: {e}")
            models = [
                {"id": "claude-3-sonnet-20240229", "name": "Claude 3 Sonnet"},
                {"id": "claude-3-opus-20240229", "name": "Claude 3 Opus"}
            ]
    elif provider == "mcp":
        if MCP_SSE_AVAILABLE:
            try:
                # Get models from MCPSSELLM
                mcp_models = MCPSSELLM.get_available_models()
                models = [{"id": model, "name": model} for model in mcp_models]

                # Add server URL option
                models.append({
                    "id": "http://0.0.0.0:9000/sse",
                    "name": "Default MCP SSE Server (http://0.0.0.0:9000/sse)"
                })
            except Exception as e:
                logger.error(f"Error getting MCP SSE models: {e}")
                models = [
                    {"id": "claude-3-5-sonnet-20240620", "name": "Claude 3.5 Sonnet"},
                    {"id": "http://0.0.0.0:9000/sse", "name": "Default MCP SSE Server"}
                ]
        else:
            models = []
            logger.warning("MCP SSE is not available. Cannot get MCP SSE models.")
    elif provider == "mcp-http":
        if MCP_HTTP_AVAILABLE:
            try:
                # Get models from MCPHTTPLLM
                mcp_models = MCPHTTPLLM.get_available_models()
                models = [{"id": model, "name": model} for model in mcp_models]

                # Add server URL option
                models.append({
                    "id": "http://0.0.0.0:9000/mcp",
                    "name": "Default MCP HTTP Server (http://0.0.0.0:9000/mcp)"
                })
            except Exception as e:
                logger.error(f"Error getting MCP HTTP models: {e}")
                models = [
                    {"id": "claude-3-5-sonnet-20240620", "name": "Claude 3.5 Sonnet"},
                    {"id": "http://0.0.0.0:9000/mcp", "name": "Default MCP HTTP Server"}
                ]
        else:
            models = []
            logger.warning("MCP HTTP is not available. Cannot get MCP HTTP models.")
    else:
        models = []

    return JsonResponse({"models": models})

@login_required
@require_http_methods(["POST"])
@csrf_exempt
def set_llm(request):
    """Set the LLM for the chat manager."""
    try:
        data = json.loads(request.body)
        provider = data.get("provider", "mock")
        model = data.get("model")

        # Clean up the old LLM if it's an MCP client
        global chat_manager
        if hasattr(chat_manager, 'llm') and chat_manager.llm:
            old_llm = chat_manager.llm
            if isinstance(old_llm, (MCPSSELLM, MCPHTTPLLM)) and hasattr(old_llm, 'cleanup'):
                try:
                    old_llm.cleanup()
                    logger.info(f"Cleaned up old {type(old_llm).__name__} instance")
                except Exception as e:
                    logger.warning(f"Error cleaning up old LLM: {e}")

        # Create the new LLM
        llm = create_llm(provider, model)

        # Update the chat manager
        chat_manager.llm = llm

        return JsonResponse({
            "success": True,
            "provider": provider,
            "model": model
        })
    except Exception as e:
        logger.error(f"Error setting LLM: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e)
        }, status=400)

@login_required
@require_http_methods(["GET"])
def list_conversations(request):
    """List available conversations for the authenticated user."""
    try:
        # Get the username of the authenticated user
        user_id = request.user.username

        # Filter conversations by user_id
        conversations = chat_manager.list_conversations(user_id=user_id)

        # Format the conversations for the frontend
        formatted = []
        for conv in conversations:
            formatted.append({
                "id": conv["conversation_id"],
                "title": conv["title"],
                "created_at": conv["created_at"],
                "message_count": conv["message_count"],
                "path": conv["relative_path"]
            })

        return JsonResponse({"conversations": formatted})
    except Exception as e:
        logger.error(f"Error listing conversations: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e)
        }, status=500)

def _format_context_data(context_data: Dict[str, Any], context_name: str = "context") -> str:
    """Format context data into a system message for the LLM.

    This uses the same formatting logic as chat_term.py for consistency.

    Args:
        context_data: The context data dictionary.
        context_name: The name/identifier for this context.

    Returns:
        A formatted string to be used as a system message.
    """
    # Start with basic information
    message_parts = [
        f"CONTEXT LOADED: {context_name}",
        f"Type: {context_data.get('type', 'unknown')}",
        f"Title: {context_data.get('title', 'No title')}",
        f"Description: {context_data.get('description', 'No description')}"
    ]

    # Add the actual data
    if 'data' in context_data:
        data = context_data['data']
        if isinstance(data, list):
            message_parts.append(f"\nData ({len(data)} items):")
            for i, item in enumerate(data, 1):
                if isinstance(item, dict):
                    # Format dictionary items nicely
                    item_str = f"  {i}. "
                    if 'name' in item:
                        item_str += f"Name: {item['name']}"
                    elif 'title' in item:
                        item_str += f"Title: {item['title']}"
                    elif 'id' in item:
                        item_str += f"ID: {item['id']}"

                    # Add other key fields
                    for key, value in item.items():
                        if key not in ['name', 'title', 'id']:
                            item_str += f", {key}: {value}"

                    message_parts.append(item_str)
                else:
                    message_parts.append(f"  {i}. {item}")
        elif isinstance(data, dict):
            message_parts.append("\nData:")
            for key, value in data.items():
                message_parts.append(f"  {key}: {value}")
        else:
            message_parts.append(f"\nData: {data}")

    message_parts.append("\nThis contextual information is now available for reference in our conversation.")

    return "\n".join(message_parts)

@login_required
@require_http_methods(["POST"])
@csrf_exempt
def create_conversation(request):
    """Create a new conversation for the authenticated user."""
    try:
        data = json.loads(request.body)
        # Use the provided title or generate a default one
        title = data.get("title")
        context_data = data.get("context")  # New: accept context data

        # If context data is provided and has a title, use it as the conversation title
        if context_data and context_data.get("title") and not title:
            title = context_data.get("title")
        elif not title:
            title = f"Conversation {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

        # Always use the authenticated user's username
        user_id = request.user.username

        # Create the conversation
        conversation = chat_manager.create_conversation(
            title=title,
            user_id=user_id
        )

        # Add context as system message if provided
        if context_data and context_data.get("data"):
            logger.info(f"Loading context data for conversation {conversation.conversation_id}")
            context_message = _format_context_data(context_data, "frontend_context")
            chat_manager.add_message("system", context_message)
        else:
            # Add a default system message, but only if not using MCP
            # (MCP/Anthropic API handles system messages differently)
            if not (isinstance(chat_manager.llm, MCPSSELLM) or isinstance(chat_manager.llm, MCPHTTPLLM)):
                chat_manager.add_message("system", "Welcome to Gaia Chat! How can I help you today?")

        # Save the conversation
        chat_manager.save_conversation()

        return JsonResponse({
            "success": True,
            "conversation": {
                "id": conversation.conversation_id,
                "title": conversation.title,
                "created_at": conversation.created_at
            }
        })
    except Exception as e:
        logger.error(f"Error creating conversation: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e)
        }, status=500)

@login_required
@require_http_methods(["GET"])
def load_conversation(request, conversation_id):
    """Load a conversation by ID, ensuring it belongs to the authenticated user."""
    try:
        # Load the conversation
        conversation = chat_manager.load_conversation(conversation_id)

        if not conversation:
            return JsonResponse({
                "success": False,
                "error": f"Conversation {conversation_id} not found"
            }, status=404)

        # Check if the conversation belongs to the authenticated user
        if conversation.user_id != request.user.username:
            return JsonResponse({
                "success": False,
                "error": "You do not have permission to access this conversation"
            }, status=403)

        # Set as active conversation
        chat_manager.active_conversation = conversation

        # Format the messages
        messages = []
        for msg in conversation.messages:
            messages.append({
                "role": msg["role"],
                "content": msg["content"],
                "timestamp": msg.get("timestamp", "")
            })

        return JsonResponse({
            "success": True,
            "conversation": {
                "id": conversation.conversation_id,
                "title": conversation.title,
                "created_at": conversation.created_at,
                "messages": messages
            }
        })
    except Exception as e:
        logger.error(f"Error loading conversation: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e)
        }, status=500)

@login_required
@require_http_methods(["DELETE"])
@csrf_exempt
def delete_conversation(request, conversation_id):
    """Delete a conversation, ensuring it belongs to the authenticated user."""
    try:
        # Load the conversation first to check ownership
        conversation = chat_manager.load_conversation(conversation_id)

        if not conversation:
            return JsonResponse({
                "success": False,
                "error": f"Conversation {conversation_id} not found"
            }, status=404)

        # Check if the conversation belongs to the authenticated user
        if conversation.user_id != request.user.username:
            return JsonResponse({
                "success": False,
                "error": "You do not have permission to delete this conversation"
            }, status=403)

        # Delete the conversation
        if chat_manager.delete_conversation(conversation_id):
            return JsonResponse({
                "success": True,
                "message": "Conversation deleted successfully"
            })
        else:
            return JsonResponse({
                "success": False,
                "error": "Failed to delete conversation"
            }, status=500)
    except Exception as e:
        logger.error(f"Error deleting conversation: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e)
        }, status=500)

@login_required
@require_http_methods(["POST"])
@csrf_exempt
def update_conversation(request, conversation_id):
    """Update a conversation with new messages, ensuring it belongs to the authenticated user."""
    try:
        data = json.loads(request.body)
        messages = data.get("messages", [])

        if not isinstance(messages, list):
            return JsonResponse({
                "success": False,
                "error": "Messages must be a list"
            }, status=400)

        # Load the conversation first to check ownership
        conversation = chat_manager.load_conversation(conversation_id)

        if not conversation:
            return JsonResponse({
                "success": False,
                "error": f"Conversation {conversation_id} not found"
            }, status=404)

        # Check if the conversation belongs to the authenticated user
        if conversation.user_id != request.user.username:
            return JsonResponse({
                "success": False,
                "error": "You do not have permission to update this conversation"
            }, status=403)

        # Set as active conversation
        chat_manager.active_conversation = conversation

        # Update the conversation messages
        conversation.messages = messages

        # Save the conversation
        chat_manager.save_conversation()

        return JsonResponse({
            "success": True,
            "message": "Conversation updated successfully",
            "conversation": {
                "id": conversation.conversation_id,
                "title": conversation.title,
                "message_count": len(conversation.messages)
            }
        })
    except Exception as e:
        logger.error(f"Error updating conversation: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e)
        }, status=500)

@login_required
@require_http_methods(["POST"])
@csrf_exempt
def send_message(request):
    """Send a message to the active conversation, ensuring it belongs to the authenticated user."""
    try:
        data = json.loads(request.body)

        # Handle both old format (message) and new format (messages + conversation_id)
        message = data.get("message", "")
        messages = data.get("messages", [])
        conversation_id = data.get("conversation_id")
        provider = data.get("provider")

        # If we have messages and conversation_id, use the new format
        if messages and conversation_id:
            # Load the conversation
            conversation = chat_manager.load_conversation(conversation_id)

            if not conversation:
                return JsonResponse({
                    "success": False,
                    "error": f"Conversation {conversation_id} not found"
                }, status=404)

            # Check if the conversation belongs to the authenticated user
            if conversation.user_id != request.user.username:
                return JsonResponse({
                    "success": False,
                    "error": "You do not have permission to send messages to this conversation"
                }, status=403)

            # Set as active conversation
            chat_manager.active_conversation = conversation

            # Get the last user message from the messages array
            user_messages = [msg for msg in messages if msg.get("role") == "user"]
            if not user_messages:
                return JsonResponse({
                    "success": False,
                    "error": "No user message found in messages"
                }, status=400)

            message = user_messages[-1].get("content", "")

            # Update the conversation with all messages (including context)
            # But exclude the last user message since process_message will add it
            conversation.messages = messages[:-1]  # Remove the last user message

        # Use old format if no messages/conversation_id provided
        if not message.strip():
            return JsonResponse({
                "success": False,
                "error": "Message cannot be empty"
            }, status=400)

        # Check if there's an active conversation
        if not chat_manager.active_conversation:
            return JsonResponse({
                "success": False,
                "error": "No active conversation",
                "code": "no_active_conversation"
            }, status=400)

        # Check if the active conversation belongs to the authenticated user
        if chat_manager.active_conversation.user_id != request.user.username:
            return JsonResponse({
                "success": False,
                "error": "You do not have permission to send messages to this conversation"
            }, status=403)

        # Process the message
        # Note: The quote handling for direct tool calls is now handled in chatobj.py
        # The process_message method will add the user message and generate a response
        response = chat_manager.process_message(message)

        # Save the conversation
        chat_manager.save_conversation()

        return JsonResponse({
            "success": True,
            "message": response  # Use "message" key to match frontend expectation
        })
    except ValueError as e:
        # This is likely due to no active conversation
        logger.error(f"Error sending message: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e),
            "code": "no_active_conversation"
        }, status=400)
    except Exception as e:
        logger.error(f"Error sending message: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e)
        }, status=500)


@login_required
@require_http_methods(["POST"])
@csrf_exempt
def send_message_stream(request):
    """Send a message with streaming progress updates for long-running tasks."""
    try:
        data = json.loads(request.body)
        message = data.get("message", "")

        if not message.strip():
            return JsonResponse({
                "success": False,
                "error": "Message cannot be empty"
            }, status=400)

        # Check if there's an active conversation
        if not chat_manager.active_conversation:
            return JsonResponse({
                "success": False,
                "error": "No active conversation",
                "code": "no_active_conversation"
            }, status=400)

        # Check if the active conversation belongs to the authenticated user
        if chat_manager.active_conversation.user_id != request.user.username:
            return JsonResponse({
                "success": False,
                "error": "You do not have permission to send messages to this conversation"
            }, status=403)

        # Check if this is an MCP provider that supports streaming
        is_mcp_provider = isinstance(chat_manager.llm, (MCPSSELLM, MCPHTTPLLM))

        if not is_mcp_provider:
            # Fall back to regular send_message for non-MCP providers
            return send_message(request)

        def generate_stream():
            """Generator function for streaming response."""
            try:
                # For now, use a simplified approach that just calls the regular MCP client
                # and simulates streaming by breaking up the response

                yield f"data: {json.dumps({'type': 'start', 'content': 'Starting task...'})}\n\n"

                # Simulate progress updates
                for i in range(1, 6):
                    # Create progress bar
                    pct = (i / 5) * 100
                    bar_length = 28
                    filled = int(bar_length * pct / 100)
                    bar = "█" * filled + "░" * (bar_length - filled)

                    progress_data = {
                        'type': 'progress',
                        'progress': i,
                        'total': 5,
                        'percentage': pct,
                        'bar': bar,
                        'message': f'//// CONTEXT PROGRESS - CUSTOM MESSAGE {i}////'
                    }
                    yield f"data: {json.dumps(progress_data)}\n\n"

                    # Small delay to simulate real progress
                    import time
                    time.sleep(1)

                # Now call the actual tool
                try:
                    # Add the user message to conversation
                    chat_manager.add_message("user", message)

                    # Process the message using the regular chat manager
                    response = chat_manager.process_message(message, add_user_message=False)

                    # Save the conversation
                    chat_manager.save_conversation()

                    # Send final result
                    yield f"data: {json.dumps({'type': 'final', 'content': response})}\n\n"
                    yield f"data: {json.dumps({'type': 'complete'})}\n\n"

                except Exception as e:
                    logger.error(f"Error processing message: {e}")
                    yield f"data: {json.dumps({'type': 'error', 'content': str(e)})}\n\n"

            except Exception as e:
                logger.error(f"Error in generate_stream: {e}")
                yield f"data: {json.dumps({'type': 'error', 'content': str(e)})}\n\n"

        # Return streaming response
        response = StreamingHttpResponse(
            generate_stream(),
            content_type='text/event-stream'
        )
        response['Cache-Control'] = 'no-cache'
        response['Access-Control-Allow-Origin'] = '*'
        return response

    except Exception as e:
        logger.error(f"Error in send_message_stream: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e)
        }, status=500)


@login_required
@require_http_methods(["GET"])
def chat_stream_get(request):
    """Handle EventSource streaming for chat messages via GET request."""
    try:
        # Get parameters from query string
        conversation_id = request.GET.get('conversation_id')
        # provider = request.GET.get('provider', 'openai')  # Not used yet

        if not conversation_id:
            return JsonResponse({
                "success": False,
                "error": "conversation_id is required"
            }, status=400)

        # Load the conversation to check ownership
        conversation = chat_manager.load_conversation(conversation_id)

        if not conversation:
            return JsonResponse({
                "success": False,
                "error": f"Conversation {conversation_id} not found"
            }, status=404)

        # Check if the conversation belongs to the authenticated user
        if conversation.user_id != request.user.username:
            return JsonResponse({
                "success": False,
                "error": "You do not have permission to access this conversation"
            }, status=403)

        # Set as active conversation
        chat_manager.active_conversation = conversation

        def generate_stream():
            """Generator function for streaming response."""
            try:
                yield f"data: {json.dumps({'type': 'ready', 'content': 'Stream ready for messages'})}\n\n"

                # Keep the connection alive and wait for messages
                # In a real implementation, this would listen for new messages
                # For now, we'll just keep the connection open
                import time
                while True:
                    time.sleep(1)
                    yield f"data: {json.dumps({'type': 'heartbeat'})}\n\n"

            except Exception as e:
                logger.error(f"Error in generate_stream: {e}")
                yield f"data: {json.dumps({'type': 'error', 'content': str(e)})}\n\n"

        # Return streaming response
        response = StreamingHttpResponse(
            generate_stream(),
            content_type='text/event-stream'
        )
        response['Cache-Control'] = 'no-cache'
        response['Access-Control-Allow-Origin'] = '*'
        return response

    except Exception as e:
        logger.error(f"Error in chat_stream_get: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e)
        }, status=500)


@login_required
@require_http_methods(["POST"])
@csrf_exempt
def chat_stream_init(request):
    """Initialize a chat stream with a message."""
    try:
        data = json.loads(request.body)
        conversation_id = data.get("conversation_id")
        messages = data.get("messages", [])
        # provider = data.get("provider", "openai")  # Not used yet

        if not conversation_id:
            return JsonResponse({
                "success": False,
                "error": "conversation_id is required"
            }, status=400)

        if not messages:
            return JsonResponse({
                "success": False,
                "error": "messages are required"
            }, status=400)

        # Load the conversation to check ownership
        conversation = chat_manager.load_conversation(conversation_id)

        if not conversation:
            return JsonResponse({
                "success": False,
                "error": f"Conversation {conversation_id} not found"
            }, status=404)

        # Check if the conversation belongs to the authenticated user
        if conversation.user_id != request.user.username:
            return JsonResponse({
                "success": False,
                "error": "You do not have permission to access this conversation"
            }, status=403)

        # Set as active conversation
        chat_manager.active_conversation = conversation

        # Get the last user message
        user_message = None
        for msg in reversed(messages):
            if msg.get("role") == "user":
                user_message = msg.get("content", "")
                break

        if not user_message:
            return JsonResponse({
                "success": False,
                "error": "No user message found"
            }, status=400)

        # Process the message
        response = chat_manager.process_message(user_message)

        # Save the conversation
        chat_manager.save_conversation()

        return JsonResponse({
            "success": True,
            "response": response
        })

    except Exception as e:
        logger.error(f"Error in chat_stream_init: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e)
        }, status=500)
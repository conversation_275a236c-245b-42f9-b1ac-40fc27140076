<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    {% csrf_token %}
    <!-- Vue.js -->
    <script src="https://cdn.jsdelivr.net/npm/vue@2.6.14/dist/vue.js"></script>
    <!-- Axios for HTTP requests -->
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <!-- Marked for Markdown rendering -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <!-- CSS -->
    <style>
        :root {
            --primary-color: #f4f5ef;
            --secondary-color: #ccc;
            --background-color: #f4f4f4;
            --text-color: #333;
            --light-text: #666;
            --border-color: #ddd;
            --success-color: #4caf50;
            --error-color: #f44336;
            --warning-color: #ff9800;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-family: Verdana, sans-serif;
            line-height: 1.6;
            background-color: var(--background-color);
            color: var(--text-color);
        }

        .app-container {
            display: flex;
            height: 100vh;
        }

        .sidebar {
            width: 300px;
            background-color: var(--primary-color);
            color: #1d1d1d;
            padding: 1rem;
            overflow-y: auto;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            margin-right: v-bind('chatContext && activeConversation ? "300px" : "0"');
        }

        .chat-container {
            flex: 1;
            padding: 1rem;
            overflow-y: auto;
            background-color: white;
        }
        .chat-inner {
            margin: 0 auto;
            max-width: 800px;
            width: 94%;
            padding: 1rem;
            overflow-y: auto;
            background-color: white;
        }
        .chat-inner h2 {
            background: url('');
            margin-bottom: 2rem;
        }

        .input-container {
            padding: 1rem;
            background-color: #f9f9f9;
            border-top: 1px solid var(--border-color);
        }

        .sidebar h2 {
            margin-bottom: 1rem;
            font-size: 1.5rem;
        }

        .sidebar h3 {
            margin: 1rem 0 0.5rem;
            font-size: 1.2rem;
            color: #1d1d1d;
        }

        .conversation-list {
            margin-top: 1rem;
        }

        .conversation-item {
            padding: 0rem 0.5rem 0rem 0.5rem;
            margin-bottom: 0.2rem;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.2s;
        }

        .conversation-content {
            flex: 1;
            cursor: pointer;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding: 0.2rem 0;
        }

        .delete-button {
            background: none;
            border: none;
            color: rgba(255, 255, 255, 0.5);
            font-size: 1.2rem;
            cursor: pointer;
            padding: 0 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 1.5rem;
            width: 1.5rem;
            border-radius: 50%;
            transition: all 0.2s;
        }

        .delete-button:hover {
            background-color: rgba(255, 0, 0, 0.2);
            color: white;
        }

        .delete-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .loading-small {
            display: inline-block;
            width: 0.8rem;
            height: 0.8rem;
            border: 2px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top-color: #666;
            animation: spin 1s ease-in-out infinite;
        }

        .conversation-item:hover {
            background-color: #ccc;
        }

        .conversation-item.active {
            background-color: var(--secondary-color);
        }

        .message {
            margin-bottom: 0.5rem;
            /*padding: 0.5rem 1rem;*/
            padding: 0rem 0.5rem;
            border-radius: 10px;
        }

        .message-user {
            background-color: #f0f0f0;
        }

        .message-assistant {

        }

        .message-system {
            margin: 0 1rem;
            font-style: italic;
        }

        .message-role {
            font-weight: bold;
            margin-bottom: 0.25rem;
        }

        .message-content {
            white-space: pre-wrap;
            padding: 0.5rem;
        }

        .input-box {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            resize: none;
            font-family: inherit;
            font-size: 1rem;
        }

        .button {
            padding: 0.5rem 1rem;
            background-color: #1d1d1d;
            color: white;
            border: none;
            /*border-radius: 4px;*/
            cursor: pointer;
            font-size: 1rem;
            margin-top: 0.5rem;
        }

        .button:hover {
            background-color: #888;
        }

        .button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }

        .select-container {
            margin-bottom: 1rem;
        }

        .select-container label {
            display: block;
            margin-bottom: 0.25rem;
            color: #666;
        }

        .select-container select {
            width: 100%;
            padding: 0.5rem;
            border-radius: 4px;
            background-color: white;
            border: 1px solid var(--border-color);
        }

        .alert {
            padding: 0.5rem;
            margin-bottom: 1rem;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        .alert-warning {
            background-color: #fff8e1;
            margin-top: 0.5rem;
            border-left: 4px solid var(--warning-color);
        }

        .alert-error {
            background-color: #ffebee;
            border-left: 4px solid var(--error-color);
        }

        .alert-success {
            background-color: #e8f5e9;
            border-left: 4px solid var(--success-color);
        }

        .loading {
            display: inline-block;
            width: 1rem;
            height: 1rem;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top-color: white;
            animation: spin 1s ease-in-out infinite;
            margin-left: 0.5rem;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        .empty-state {
            text-align: center;
            padding: 2rem;
            color: var(--light-text);
        }

        .user-info {
            margin-bottom: 1rem;
            padding: 1rem;
            background-color: #caf22a;
            border-radius: 0px;
        }

        .user-info p {
            margin-bottom: 0.5rem;
            font-weight: bold;
        }

        .user-links {
            display: flex;
            justify-content: space-between;
        }

        .user-link {
            color: var(--text-color);
            text-decoration: none;
            font-size: 0.9rem;
        }

        .user-link:hover {
            color: var(--text-color);
            text-decoration: underline;
        }
        .logo {
            margin-bottom: 1rem;
        }

        .context-content-main {
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
        }

        .chat-sidebar {
            position: fixed;
            right: 0;
            top: 0;
            width: 450px; /* Increased from 300px to 450px (150% wider) */
            padding: 20px;
            height: 100vh;
            background: #f8f9fa;
            border-left: 1px solid #ddd;
            display: flex;
            flex-direction: column;
            z-index: 1000;
        }

        .chat-sidebar-header {
            margin-bottom: 15px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }

        .chat-sidebar-header h3 {
            margin: 0;
            color: #2c3e50;
            font-size: 1.2em;
            font-weight: 600;
        }

        .chat-messages-container {
            flex: 1;
            overflow-y: auto;
            margin-bottom: 15px;
            display: flex;
            flex-direction: column;
        }

        /* Completely redesigned scrollbar handling */
        .chat-messages-container {
            flex: 1;
            overflow-y: auto;
            margin-bottom: 15px;
            display: flex;
            flex-direction: column;
        }

        /* Make message containers fully fluid */
        .message-compact {
            margin-bottom: 8px;
            padding: 5px;
            border-radius: 5px;
            font-size: 0.9rem;
            word-wrap: break-word;
            overflow-wrap: break-word;
            overflow: visible; /* Ensure no scrollbars appear */
            width: 100%;
        }

        .message-role-compact {
            font-weight: bold;
            font-size: 0.8rem;
        }

        .message-content-compact {
            white-space: pre-wrap;
            overflow: visible !important; /* Force no scrollbars */
            font-size: 0.9rem;
            max-height: none;
            width: 100%;
            display: block;
        }

        /* Completely disable scrollbars on message content */
        .message-content-compact::-webkit-scrollbar,
        .message-compact::-webkit-scrollbar {
            width: 0 !important;
            height: 0 !important;
            display: none !important;
        }

        .message-content-compact,
        .message-compact {
            -ms-overflow-style: none !important;  /* IE and Edge */
            scrollbar-width: none !important;  /* Firefox */
        }

        /* Ensure all content inside messages wraps properly */
        .message-content-compact * {
            max-width: 100%;
            word-wrap: break-word;
            overflow-wrap: break-word;
            white-space: pre-wrap;
        }

        /* Special handling for code blocks */
        .message-content-compact pre,
        .message-content-compact code {
            white-space: pre-wrap !important;
            word-wrap: break-word !important;
            overflow-wrap: break-word !important;
            max-width: 100% !important;
            overflow: visible !important;
        }

        /* Ensure tables don't cause horizontal scrolling */
        .message-content-compact table {
            max-width: 100%;
            table-layout: fixed;
            width: 100%;
        }

        .message-content-compact td,
        .message-content-compact th {
            word-break: break-word;
        }

        .input-container-sidebar {
            padding: 10px 0;
            border-top: 1px solid var(--border-color);
        }

        /* Adjust main content when context sidebar is present */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            margin-right: v-bind('chatContext && activeConversation ? "300px" : "0"');
        }

        /* Add these styles to handle code blocks and pre-formatted text */
        .message-content-compact pre,
        .message-content-compact code {
            white-space: pre-wrap;
            word-wrap: break-word;
            overflow-wrap: break-word;
            max-width: 100%;
            overflow-x: visible;
        }

        /* Ensure images don't overflow */
        .message-content-compact img {
            max-width: 100%;
            height: auto;
        }

        /* Ensure tables don't cause horizontal scrolling */
        .message-content-compact table {
            max-width: 100%;
            display: block;
            overflow-x: auto;
        }

        /* Add styles for context info banner */
        .context-info-banner {
            margin: 15px 0;
            padding: 10px 15px;
            background-color: #e8f5e9;
            border-left: 4px solid var(--success-color);
            border-radius: 4px;
        }

        .context-info-banner p {
            margin: 5px 0;
        }

        /* Context content styling */
        .context-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .context-item {
            background: #ffffff;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .context-item:hover {
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
            transform: translateY(-2px);
        }

        .context-item h4 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            font-size: 1.3em;
            font-weight: 600;
            border-bottom: 2px solid #3498db;
            padding-bottom: 8px;
        }

        .context-details {
            margin-bottom: 15px;
        }

        .context-details p {
            margin: 8px 0;
            display: flex;
            align-items: center;
            font-size: 0.95em;
        }

        .context-details strong {
            color: #34495e;
            min-width: 90px;
            font-weight: 600;
            margin-right: 8px;
        }

        .context-description {
            color: #555;
            font-style: italic;
            line-height: 1.5;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #ecf0f1;
        }

        /* Industry-specific color coding */
        .context-item[data-industry="Manufacturing"] h4 {
            border-bottom-color: #e74c3c;
        }

        .context-item[data-industry="Technology"] h4 {
            border-bottom-color: #3498db;
        }

        .context-item[data-industry="Renewable Energy"] h4 {
            border-bottom-color: #27ae60;
        }

        /* Responsive design for smaller screens */
        @media (max-width: 768px) {
            .context-content {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .context-item {
                padding: 15px;
            }
        }

        /* Add subtle animation for loading states */
        .context-item.loading {
            opacity: 0.7;
            pointer-events: none;
        }

        /* Improve readability with better typography */
        .context-item h4 {
            line-height: 1.3;
        }

        .context-details p {
            line-height: 1.4;
        }

        /* JSON fallback styling */
        .context-json {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.85em;
            line-height: 1.4;
            overflow-x: auto;
            white-space: pre-wrap;
            color: #495057;
        }

        /* Add a subtle gradient background to the context area */
        .context-content-main {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 12px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        /* Sidebar chat styling to match main chat interface */
        .sidebar-message {
            margin-bottom: 0.5rem;
            padding: 0rem 0.5rem;
            border-radius: 10px;
        }

        .sidebar-message.message-user {
            background-color: #f0f0f0;
        }

        .sidebar-message.message-assistant {
            background-color: transparent;
        }

        .sidebar-message.message-system {
            margin: 0 1rem;
            font-style: italic;
        }

        .sidebar-role {
            font-weight: bold;
            margin-bottom: 0.25rem;
            display: block;
        }

        .sidebar-content {
            white-space: pre-wrap;
            padding: 0.5rem;
        }

        .sidebar-input-area {
            padding: 1rem;
            background-color: #f9f9f9;
            border-top: 1px solid var(--border-color);
        }

        .sidebar-textarea {
            width: 100%;
            padding: 0.75rem;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            resize: none;
            font-family: inherit;
            font-size: 1rem;
            margin-bottom: 0.5rem;
        }

        .sidebar-button {
            padding: 0.5rem 1rem;
            background-color: #1d1d1d;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            width: 100%;
        }

        .sidebar-button:hover {
            background-color: #333;
        }

        .sidebar-button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div id="app" class="app-container">
        <div class="sidebar">
            <div class="logo">
                <img src="https://agfunder-gaia-assets.s3.us-west-2.amazonaws.com/agfunder_logo.svg" alt="AgFunder Logo">
              </div>
            <h2>Gaia Chat</h2>

            <div style="margin-bottom: 1.5rem">
                <img src="https://agfunder-gaia-assets.s3.us-west-2.amazonaws.com/AI-in-agriculture-istock-Igor-Borisenko.jpg" width="100%" height="100%" alt="Gaia Chat Banner" style="border-radius: 2px">
              </div>

            <!-- User Info -->
            <div class="user-info">
                <p>Welcome, {{ user.username }}</p>
                <div class="user-links">
                    <a href="{% url 'gaia_chat:profile' %}" class="user-link">Profile</a>
                    <a href="{% url 'gaia_chat:logout' %}" class="user-link">Logout</a>
                </div>
            </div>

            <!-- LLM Settings -->
             {% if user.is_superuser %}
            <h3>LLM Settings</h3>
            <div class="select-container">
                <label for="llm-provider">Provider</label>
                <select id="llm-provider" v-model="selectedProvider" @change="onProviderChange">
                    <option v-for="provider in providers" :key="provider.id" :value="provider.id">
                        [[ provider.name ]]
                    </option>
                </select>
            </div>

            <div class="select-container">
                <label for="llm-model">Model</label>
                <select id="llm-model" v-model="selectedModel" @change="setLLM" :disabled="!models.length || isSettingLLM">
                    <option v-for="model in models" :key="model.id" :value="model.id">
                        [[ model.name ]]
                    </option>
                </select>
            </div>

            <!-- LLM Status Info -->
            <div style="margin-top: 0.5rem; font-size: 0.9rem;">
                <div v-if="isSettingLLM" style="color: #666;">
                    <span class="loading-small" style="margin-right: 0.5rem;"></span>
                    Setting LLM...
                </div>
                <div v-else-if="selectedProvider === 'mcp' || selectedProvider === 'mcp-http'">
                    <span v-if="selectedProvider === 'mcp'">✓ Using MCP SSE protocol</span>
                    <span v-if="selectedProvider === 'mcp-http'">✓ Using MCP HTTP protocol</span>
                </div>
                <div v-else style="color: #666;">
                    ✓ LLM ready
                </div>
            </div>

            <!-- API Key Warnings -->
            <div v-if="selectedProvider === 'openai' && !hasOpenAIKey" class="alert alert-warning">
                OpenAI API key not found. Set the OPENAI_API_KEY environment variable.
            </div>

            <div v-if="selectedProvider === 'anthropic' && !hasAnthropicKey" class="alert alert-warning">
                Anthropic API key not found. Set the ANTHROPIC_API_KEY environment variable.
            </div>
            {% endif %}

            <!-- Conversation Management -->

            <!-- New Conversation Form
            <h3>New Conversation</h3>-->
            <div style="margin-bottom: 2rem;"></div>
            <div class="new-conversation-form">
                <input
                    type="text"
                    v-model="newConversationTitle"
                    placeholder="Enter conversation title"
                    class="input-box"
                    style="margin-bottom: 0.5rem;"
                />
                <button class="button" @click="createConversation" :disabled="isCreatingConversation">
                    New Conversation
                    <span v-if="isCreatingConversation" class="loading"></span>
                </button>
            </div>

            <div style="margin-bottom: 2rem;"></div>

            <h3>Your Conversations</h3>
            <div class="conversation-list">
                <div v-if="conversations.length === 0" class="empty-state">
                    <p>You don't have any conversations yet</p>
                    <p>Create a new conversation to get started</p>
                </div>
                <div
                    v-for="conv in conversations"
                    :key="conv.id"
                    class="conversation-item"
                    :class="{ active: activeConversation && activeConversation.id === conv.id }"
                    :title="formatDate(conv.created_at)"
                >
                    <div class="conversation-content" @click="loadConversation(conv.id)">
                        [[ conv.title ]]
                    </div>
                    <button
                        class="delete-button"
                        @click.stop="deleteConversation(conv.id)"
                        :disabled="isDeletingConversation === conv.id"
                        :title="'Delete conversation'"
                    >
                        <span v-if="isDeletingConversation === conv.id" class="loading-small"></span>
                        <span v-else>&times;</span>
                    </button>
                </div>
            </div>
        </div>

        <div class="main-content">
            <div class="chat-container" ref="chatContainer">
                <!-- Error display -->
                <div v-if="error" class="alert alert-error">
                    [[ error ]]
                </div>

                <div v-if="!activeConversation" class="empty-state">
                    <h3>No active conversation</h3>
                    <p>Create a new conversation or select an existing one to start chatting.</p>
                </div>

                <div v-else-if="chatContext" class="chat-inner">
                    <!-- Chat context moved to main content area -->
                    <div class="context-content-main">
                        <div class="context-header">
                            <h3>[[ chatContext.title ]]</h3>
                            <p class="context-description">[[ chatContext.description ]]</p>
                            <div class="context-info-banner">
                                <p>All context data has been automatically loaded into the conversation.</p>
                                <p>You can now ask questions about this information directly.</p>
                            </div>
                        </div>
                        
                        <!-- Companies context type -->
                        <div v-if="chatContext.type === 'companies'" class="context-content">
                            <div v-for="item in chatContext.data" :key="item.id"
                                 class="context-item"
                                 :data-industry="item.industry">
                                <h4>[[ item.name ]]</h4>
                                <div class="context-details">
                                    <p v-if="item.industry">
                                        <strong>🏭 Industry:</strong> [[ item.industry ]]
                                    </p>
                                    <p v-if="item.founded">
                                        <strong>📅 Founded:</strong> [[ item.founded ]]
                                    </p>
                                    <p v-if="item.employees">
                                        <strong>👥 Employees:</strong> [[ item.employees ]]
                                    </p>
                                    <p v-if="item.revenue">
                                        <strong>💰 Revenue:</strong> [[ item.revenue ]]
                                    </p>
                                </div>
                                <p v-if="item.description" class="context-description">[[ item.description ]]</p>
                            </div>
                        </div>
                        
                        <!-- Documents context type -->
                        <div v-else-if="chatContext.type === 'documents'" class="context-content">
                            <div v-for="item in chatContext.data" :key="item.id" class="context-item">
                                <h4>[[ item.title ]]</h4>
                                <div class="context-details">
                                    <p v-if="item.date">
                                        <strong>📅 Date:</strong> [[ item.date ]]
                                    </p>
                                    <p v-if="item.author">
                                        <strong>✍️ Author:</strong> [[ item.author ]]
                                    </p>
                                </div>
                                <p v-if="item.summary" class="context-description">[[ item.summary ]]</p>
                            </div>
                        </div>
                        
                        <!-- Generic context type - fallback for any other type -->
                        <div v-else class="context-content">
                            <div v-for="(item, index) in chatContext.data" :key="index" class="context-item">
                                <pre class="context-json">[[ JSON.stringify(item, null, 2) ]]</pre>
                            </div>
                        </div>
                    </div>
                </div>
                <div v-else class="chat-inner">
                    <h2>
                        [[ activeConversation.title ]]
                        <small v-if="mcpProtocol" style="font-size: 0.7em; color: #666; font-weight: normal;">
                            (MCP [[ mcpProtocol ]])
                        </small>
                    </h2>

                    <div v-for="(message, index) in activeConversation.messages" :key="index"
                        class="message"
                        :class="{
                            'message-user': message.role === 'user',
                            'message-assistant': message.role === 'assistant',
                            'message-system': message.role === 'system'
                        }"
                    >
                        <div class="message-role">[[ formatRole(message.role) ]]</div>
                        <div class="message-content" v-html="formatMessage(message.content)"></div>
                    </div>
                </div>
            </div>

            <!-- Replace the entire chat sidebar with a simpler version -->
            <div v-if="chatContext && activeConversation" class="chat-sidebar">
                <div class="chat-sidebar-header">
                    <h3>[[ activeConversation.title ]]</h3>
                    <small v-if="mcpProtocol" style="font-size: 0.7em; color: #666; font-weight: normal;">
                        (MCP [[ mcpProtocol ]])
                    </small>
                </div>
                
                <!-- Chat messages container with proper styling -->
                <div class="chat-messages-container" ref="sidebarChatContainer">
                    <!-- Message structure matching main chat interface -->
                    <div v-for="(message, index) in activeConversation.messages" :key="index"
                        class="sidebar-message"
                        :class="{
                            'message-user': message.role === 'user',
                            'message-assistant': message.role === 'assistant',
                            'message-system': message.role === 'system'
                        }"
                    >
                        <div class="sidebar-role">[[ formatRole(message.role) ]]</div>
                        <div class="sidebar-content" v-html="formatMessage(message.content)"></div>
                    </div>
                </div>
                
                <!-- Input area matching main chat interface -->
                <div class="sidebar-input-area">
                    <textarea
                        class="sidebar-textarea"
                        v-model="userInput"
                        placeholder="Type your message here... (Try 'long_task' for streaming progress)"
                        :disabled="!activeConversation || isSendingMessage || isStreamingMessage"
                        @keydown.enter.ctrl="sendMessage"
                        rows="3"
                    ></textarea>
                    <button
                        class="sidebar-button"
                        @click="sendMessage"
                        :disabled="!activeConversation || !userInput.trim() || isSendingMessage || isStreamingMessage"
                    >
                        <span v-if="isStreamingMessage">Streaming...</span>
                        <span v-else-if="isSendingMessage">Sending...</span>
                        <span v-else>Send</span>
                        <span v-if="isSendingMessage || isStreamingMessage" class="loading"></span>
                    </button>
                    <small>Press Ctrl+Enter to send</small>
                </div>
            </div>

            <!-- Keep the original input container for when there's no context -->
            <div v-if="!chatContext" class="input-container">
                <textarea
                    class="input-box"
                    v-model="userInput"
                    placeholder="Type your message here... (Try 'long_task' for streaming progress)"
                    :disabled="!activeConversation || isSendingMessage || isStreamingMessage"
                    @keydown.enter.ctrl="sendMessage"
                    rows="3"
                ></textarea>
                <button
                    class="button"
                    @click="sendMessage"
                    :disabled="!activeConversation || !userInput.trim() || isSendingMessage || isStreamingMessage"
                >
                    <span v-if="isStreamingMessage">Streaming...</span>
                    <span v-else-if="isSendingMessage">Sending...</span>
                    <span v-else>Send</span>
                    <span v-if="isSendingMessage || isStreamingMessage" class="loading"></span>
                </button>
                <small>Press Ctrl+Enter to send</small>
            </div>

            <!-- Remove the original context sidebar since we've moved it -->
            <!-- <div v-if="chatContext" class="context-sidebar">...</div> -->
        </div>
    </div>

    <script>
        // Configure Vue to use [[ ]] for interpolation to avoid conflict with Django templates
        Vue.config.delimiters = ['[[', ']]'];

        new Vue({
            el: '#app',
            delimiters: ['[[', ']]'],
            data: {
                // LLM settings
                providers: [],
                models: [],
                selectedProvider: 'mcp-http',
                selectedModel: '',
                hasOpenAIKey: {{ has_openai_key|yesno:"true,false" }},
                hasAnthropicKey: {{ has_anthropic_key|yesno:"true,false" }},
                isSettingLLM: false,
                mcpProtocol: '',  // Will store the MCP protocol (SSE or HTTP)

                // Conversations
                conversations: [],
                activeConversation: null,
                newConversationTitle: '',
                isCreatingConversation: false,
                isLoadingConversation: false,
                isDeletingConversation: null,

                // Messaging
                userInput: '',
                isSendingMessage: false,
                isStreamingMessage: false,
                streamingProgress: [],
                streamingInfo: [],

                // Errors
                error: null,

                // Chat context
                chatContext: {{ chat_context|default:"null"|safe }},

                // Add this to the data object
                sidebarChatContainer: null
            },
            mounted() {
                this.fetchProviders();
                this.fetchConversations();

                // Check if there's a conversation ID in the URL and load it
                this.checkAndLoadConversationFromUrl();

                // Set reference to sidebar chat container
                this.$nextTick(() => {
                    this.sidebarChatContainer = document.querySelector('.chat-messages-container');
                });
            },
            updated() {
                this.scrollToBottom();
            },
            methods: {
                // URL handling
                checkAndLoadConversationFromUrl() {
                    // Check if there's a conversation parameter in the URL
                    const urlParams = new URLSearchParams(window.location.search);
                    const conversationId = urlParams.get('conversation');

                    if (conversationId) {
                        console.log('Found conversation ID in URL:', conversationId);
                        // Wait a bit for conversations to be fetched, then load the conversation
                        setTimeout(() => {
                            this.loadConversation(conversationId);
                        }, 500);
                    }
                },

                // API calls
                async fetchProviders() {
                    try {
                        const response = await axios.get('/gaia_chat/api/llm/providers/');
                        this.providers = response.data.providers;

                        // Set default provider to MCP HTTP if available, otherwise fallback to mock
                        const mcpHttpProvider = this.providers.find(p => p.id === 'mcp-http');
                        if (mcpHttpProvider) {
                            this.selectedProvider = 'mcp-http';
                        } else {
                            this.selectedProvider = 'mock';
                        }

                        await this.onProviderChange();
                    } catch (error) {
                        console.error('Error fetching providers:', error);
                    }
                },

                async onProviderChange() {
                    try {
                        const response = await axios.get('/gaia_chat/api/llm/models/', {
                            params: { provider: this.selectedProvider }
                        });
                        this.models = response.data.models;
                        if (this.models.length > 0) {
                            this.selectedModel = this.models[0].id;
                            // Automatically set the LLM when provider changes
                            await this.setLLM();
                        }
                    } catch (error) {
                        console.error('Error fetching models:', error);
                        this.models = [];
                    }
                },

                async setLLM() {
                    this.isSettingLLM = true;
                    try {
                        // Set the MCP protocol based on the selected provider
                        if (this.selectedProvider === 'mcp') {
                            this.mcpProtocol = 'SSE';
                        } else if (this.selectedProvider === 'mcp-http') {
                            this.mcpProtocol = 'HTTP';
                        } else {
                            this.mcpProtocol = '';
                        }

                        await axios.post('/gaia_chat/api/llm/set/', {
                            provider: this.selectedProvider,
                            model: this.selectedModel
                        });
                    } catch (error) {
                        console.error('Error setting LLM:', error);
                    } finally {
                        this.isSettingLLM = false;
                    }
                },

                async fetchConversations() {
                    try {
                        const response = await axios.get('/gaia_chat/api/conversations/');
                        this.conversations = response.data.conversations;
                    } catch (error) {
                        console.error('Error fetching conversations:', error);
                    }
                },

                async createConversation() {
                    this.isCreatingConversation = true;
                    try {
                        console.log('Creating new conversation...');

                        // Use the custom title if provided, otherwise use a default title
                        const title = this.newConversationTitle.trim()
                            ? this.newConversationTitle.trim()
                            : `Conversation ${new Date().toLocaleString()}`;

                        console.log('Using title:', title);

                        // Prepare the request payload
                        const payload = { title: title };

                        // If we have context data, include it in the request
                        if (this.chatContext && this.chatContext.data && this.chatContext.data.length) {
                            console.log('Including context data in conversation creation...');
                            payload.context = this.chatContext;
                        }

                        const response = await axios.post('/gaia_chat/api/conversations/create/', payload);

                        console.log('Create conversation response:', response.data);

                        if (response.data.success) {
                            // Clear the title input
                            this.newConversationTitle = '';

                            // Refresh the conversation list
                            await this.fetchConversations();

                            // Get the newly created conversation ID
                            const newConversationId = response.data.conversation.id;
                            console.log('New conversation ID:', newConversationId);

                            // Load the new conversation (context will already be loaded by backend)
                            await this.loadConversation(newConversationId);
                            console.log('Loaded conversation:', this.activeConversation);
                        }
                    } catch (error) {
                        console.error('Error creating conversation:', error);
                        if (error.response) {
                            console.error('Response status:', error.response.status);
                            console.error('Response data:', error.response.data);
                        }
                        this.error = "Error creating conversation: " + (error.response?.data?.error || error.message);
                    } finally {
                        this.isCreatingConversation = false;
                    }
                },

                async loadConversation(id) {
                    this.isLoadingConversation = true;
                    try {
                        const response = await axios.get(`/gaia_chat/api/conversations/${id}/`);
                        if (response.data.success) {
                            this.activeConversation = response.data.conversation;
                            
                            // Make sure the conversation has a messages array
                            if (!this.activeConversation.messages) {
                                this.activeConversation.messages = [];
                            }
                            
                            // Update the URL to include the conversation ID while preserving the current path
                            const currentPath = window.location.pathname;
                            const newUrl = `${currentPath}?conversation=${id}`;
                            window.history.pushState({}, '', newUrl);
                            
                            this.$nextTick(() => {
                                this.scrollToBottom();
                            });
                        }
                    } catch (error) {
                        console.error('Error loading conversation:', error);
                        this.error = "Error loading conversation: " + (error.response?.data?.error || error.message);
                    } finally {
                        this.isLoadingConversation = false;
                    }
                },

                async sendMessage() {
                    if (!this.userInput.trim() || !this.activeConversation) return;

                    const message = this.userInput;
                    this.userInput = '';

                    // Check if this is a streaming-capable message (like "long_task")
                    const isStreamingMessage = this.selectedProvider === 'mcp' || this.selectedProvider === 'mcp-http';
                    const isLongTaskMessage = message.trim().toLowerCase().includes('long_task');

                    if (isStreamingMessage && isLongTaskMessage) {
                        await this.sendStreamingMessage(message);
                    } else {
                        await this.sendRegularMessage(message);
                    }
                },

                async sendRegularMessage(message) {
                    this.isSendingMessage = true;
                    
                    try {
                        // Add the user message to the conversation
                        this.activeConversation.messages.push({
                            role: 'user',
                            content: message,
                            timestamp: new Date().toISOString()
                        });
                        
                        // Prepare the messages to send to the API
                        let messagesToSend = [...this.activeConversation.messages];
                        
                        // Send the messages to the API
                        const response = await axios.post('/gaia_chat/api/chat/', {
                            conversation_id: this.activeConversation.id,
                            messages: messagesToSend,
                            provider: this.selectedProvider
                        });
                        
                        // Add the assistant's response to the conversation
                        this.activeConversation.messages.push({
                            role: 'assistant',
                            content: response.data.message,
                            timestamp: new Date().toISOString()
                        });
                        
                        // Save the conversation using the new method
                        await this.updateConversation(this.activeConversation.id, this.activeConversation.messages);
                        
                        this.scrollToBottom();
                    } catch (error) {
                        console.error('Error sending message:', error);
                        this.error = "Error sending message: " + (error.response?.data?.error || error.message);
                    } finally {
                        this.isSendingMessage = false;
                    }
                },

                async sendStreamingMessage(message) {
                    this.isStreamingMessage = true;
                    
                    try {
                        // Add the user message to the conversation
                        this.activeConversation.messages.push({
                            role: 'user',
                            content: message,
                            timestamp: new Date().toISOString()
                        });
                        
                        // Add a placeholder for the assistant's response
                        this.activeConversation.messages.push({
                            role: 'assistant',
                            content: '',
                            timestamp: new Date().toISOString()
                        });
                        
                        // Prepare the messages to send to the API
                        let messagesToSend = [...this.activeConversation.messages];
                        messagesToSend.pop(); // Remove the placeholder
                        
                        // Set up the EventSource for streaming
                        const params = new URLSearchParams({
                            conversation_id: this.activeConversation.id,
                            provider: this.selectedProvider
                        });
                        
                        const eventSource = new EventSource(`/gaia_chat/api/chat/stream/?${params.toString()}`);
                        
                        // Send the messages to initialize the stream
                        await axios.post('/gaia_chat/api/chat/stream/init/', {
                            conversation_id: this.activeConversation.id,
                            messages: messagesToSend,
                            provider: this.selectedProvider
                        });
                        
                        // Handle the streaming response
                        eventSource.onmessage = (event) => {
                            const data = JSON.parse(event.data);
                            if (data.type === 'content') {
                                // Update the assistant's response
                                this.activeConversation.messages[this.activeConversation.messages.length - 1].content += data.content;
                                this.scrollToBottom();
                            } else if (data.type === 'done') {
                                // Stream is complete
                                eventSource.close();
                                this.isStreamingMessage = false;
                                
                                // Save the conversation using the new method
                                this.updateConversation(this.activeConversation.id, this.activeConversation.messages)
                                    .catch(error => {
                                        console.error('Error saving conversation after streaming:', error);
                                        this.error = "Error saving conversation";
                                    });
                            }
                        };
                        
                        eventSource.onerror = (error) => {
                            console.error('EventSource error:', error);
                            eventSource.close();
                            this.isStreamingMessage = false;
                            this.error = "Error streaming message";
                        };
                    } catch (error) {
                        console.error('Error sending streaming message:', error);
                        this.error = "Error sending streaming message: " + (error.response?.data?.error || error.message);
                        this.isStreamingMessage = false;
                    }
                },

                async handleStreamingData(data, assistantMessageIndex) {
                    switch (data.type) {
                        case 'start':
                            this.activeConversation.messages[assistantMessageIndex].content = '🚀 ' + data.content;
                            break;

                        case 'progress':
                            // Add progress update to the list
                            this.streamingProgress.push(data);

                            // Update assistant message with latest progress
                            const progressText = `📊 [${data.bar}] ${data.percentage.toFixed(1)}% ${data.progress}/${data.total}`;
                            const progressMessage = data.message ? ` – ${data.message}` : '';

                            // Build content with all progress updates
                            let content = '🚀 Task in progress...\n\n';
                            this.streamingProgress.forEach(p => {
                                const pText = `📊 [${p.bar}] ${p.percentage.toFixed(1)}% ${p.progress}/${p.total}`;
                                const pMessage = p.message ? ` – ${p.message}` : '';
                                content += pText + pMessage + '\n';
                            });

                            this.activeConversation.messages[assistantMessageIndex].content = content;
                            break;

                        case 'info':
                            // Add info message to the list
                            this.streamingInfo.push(data);
                            break;

                        case 'final':
                            // Update with final result
                            let finalContent = '';

                            // Add progress summary if any
                            if (this.streamingProgress.length > 0) {
                                finalContent += '✅ Task completed successfully!\n\n';
                                finalContent += `📊 Progress updates: ${this.streamingProgress.length}\n`;
                                finalContent += `ℹ️ Info messages: ${this.streamingInfo.length}\n\n`;
                            }

                            finalContent += data.content;

                            this.activeConversation.messages[assistantMessageIndex].content = finalContent;
                            this.activeConversation.messages[assistantMessageIndex].streaming = false;
                            break;

                        case 'complete':
                            // Task completed
                            this.activeConversation.messages[assistantMessageIndex].streaming = false;
                            break;

                        case 'error':
                            this.activeConversation.messages[assistantMessageIndex].content = `❌ Error: ${data.content}`;
                            this.activeConversation.messages[assistantMessageIndex].streaming = false;
                            this.error = data.content;
                            break;
                    }

                    this.scrollToBottom();
                },

                // Utility methods
                formatDate(dateString) {
                    if (!dateString) return '';
                    const date = new Date(dateString);
                    return date.toLocaleString();
                },

                formatRole(role) {
                    if (role === 'user') return "{{ user.username|default:'You'|escapejs }}";
                    if (role === 'assistant') return 'AgFunder Gaia';
                    if (role === 'system') return 'AgFunder Gaia';
                    return role;
                },

                formatMessage(content) {
                    // Check if the content contains table data in JSON format
                    if (this.isTableData(content)) {
                        return this.formatTableData(content);
                    }

                    // Use marked to render markdown for regular content
                    return marked.parse(content);
                },

                isTableData(content) {
                    // Check if content contains JSON table data
                    try {
                        // Look for table data patterns in the content
                        const jsonMatch = content.match(/\{[^}]*"type"\s*:\s*"table"[^}]*\}/);
                        if (jsonMatch) {
                            const tableData = JSON.parse(jsonMatch[0]);
                            return tableData.type === 'table' && tableData.headers && tableData.rows;
                        }
                        return false;
                    } catch (e) {
                        return false;
                    }
                },

                formatTableData(content) {
                    try {
                        // Extract JSON table data from the content
                        const jsonMatch = content.match(/\{[^}]*"type"\s*:\s*"table"[^}]*\}/);
                        if (!jsonMatch) {
                            return marked.parse(content);
                        }

                        const tableData = JSON.parse(jsonMatch[0]);

                        // Build HTML table
                        let html = '';

                        // Add title if present
                        if (tableData.title) {
                            html += `<h4 style="margin-bottom: 10px; color: #333;">${tableData.title}</h4>`;
                        }

                        html += '<table style="border-collapse: collapse; width: 100%; margin: 10px 0; border: 1px solid #ddd;">';

                        // Add headers
                        if (tableData.headers && tableData.headers.length > 0) {
                            html += '<thead><tr>';
                            tableData.headers.forEach(header => {
                                html += `<th style="border: 1px solid #ddd; padding: 8px; background-color: #f5f5f5; text-align: left; font-weight: bold;">${header}</th>`;
                            });
                            html += '</tr></thead>';
                        }

                        // Add rows
                        if (tableData.rows && tableData.rows.length > 0) {
                            html += '<tbody>';
                            tableData.rows.forEach((row, rowIndex) => {
                                const bgColor = rowIndex % 2 === 0 ? '#ffffff' : '#f9f9f9';
                                html += `<tr style="background-color: ${bgColor};">`;
                                row.forEach(cell => {
                                    html += `<td style="border: 1px solid #ddd; padding: 8px;">${cell}</td>`;
                                });
                                html += '</tr>';
                            });
                            html += '</tbody>';
                        }

                        html += '</table>';

                        // Process the rest of the content (remove the JSON part and render as markdown)
                        const remainingContent = content.replace(jsonMatch[0], '').trim();
                        if (remainingContent) {
                            html += marked.parse(remainingContent);
                        }

                        return html;
                    } catch (e) {
                        console.error('Error formatting table data:', e);
                        // Fallback to regular markdown rendering
                        return marked.parse(content);
                    }
                },

                scrollToBottom() {
                    this.$nextTick(() => {
                        // Scroll main chat container
                        if (this.$refs.chatContainer) {
                            this.$refs.chatContainer.scrollTop = this.$refs.chatContainer.scrollHeight;
                        }
                        
                        // Scroll sidebar chat container using ref
                        if (this.$refs.sidebarChatContainer) {
                            this.$refs.sidebarChatContainer.scrollTop = this.$refs.sidebarChatContainer.scrollHeight;
                        }
                    });
                },

                generateTitleFromMessages(messages) {
                    if (!messages || !messages.length) return null;

                    // Find the first user message
                    const userMessages = messages.filter(msg => msg.role === 'user');
                    if (!userMessages.length) return null;

                    const firstUserMessage = userMessages[0].content;
                    if (!firstUserMessage) return null;

                    // Extract the first 4 words (or fewer if the message is shorter)
                    const words = firstUserMessage.split(/\s+/).filter(word => word.trim().length > 0).slice(0, 4);
                    if (!words.length) return null;

                    let title = words.join(' ');

                    // Add ellipsis if the message is longer than 4 words
                    if (firstUserMessage.split(/\s+/).filter(word => word.trim().length > 0).length > 4) {
                        title += '...';
                    }

                    return title;
                },

                async deleteConversation(conversationId) {
                    this.isDeletingConversation = conversationId;
                    this.error = null;

                    try {
                        const response = await axios.delete(`/gaia_chat/api/conversations/${conversationId}/delete/`);

                        if (response.data.success) {
                            // If the deleted conversation was the active one, clear it
                            if (this.activeConversation && this.activeConversation.id === conversationId) {
                                this.activeConversation = null;
                            }

                            // Remove the conversation from the list
                            this.conversations = this.conversations.filter(conv => conv.id !== conversationId);
                        }
                    } catch (error) {
                        console.error('Error deleting conversation:', error);
                        if (error.response && error.response.status === 403) {
                            // Permission error
                            this.error = "You do not have permission to delete this conversation";
                        } else if (error.response && error.response.status === 404) {
                            // Not found error
                            this.error = "Conversation not found";
                            // Remove it from the list anyway
                            this.conversations = this.conversations.filter(conv => conv.id !== conversationId);
                        } else {
                            this.error = "Error deleting conversation";
                        }
                    } finally {
                        this.isDeletingConversation = null;
                    }
                },



                // Fix the updateConversation method to ensure proper URL formatting
                async updateConversation(conversationId, messages) {
                    try {
                        // Make sure we have a valid conversation ID
                        if (!conversationId) {
                            console.error('Cannot update conversation: No conversation ID provided');
                            return false;
                        }
                        
                        // Make sure we have messages
                        if (!messages || !Array.isArray(messages)) {
                            console.error('Cannot update conversation: Invalid messages array');
                            return false;
                        }
                        
                        // Use the conversation ID as is, without any formatting
                        console.log(`Updating conversation with ID: ${conversationId}`);
                        console.log(`URL: /gaia_chat/api/conversations/${conversationId}/update/`);
                        
                        // Send the update request
                        const response = await axios.post(`/gaia_chat/api/conversations/${conversationId}/update/`, {
                            messages: messages
                        });
                        
                        console.log('Update response:', response.data);
                        
                        return response.data.success;
                    } catch (error) {
                        console.error(`Error updating conversation ${conversationId}:`, error);
                        
                        // Log detailed error information
                        if (error.response) {
                            console.error('Response status:', error.response.status);
                            console.error('Response data:', error.response.data);
                        }
                        
                        // If the conversation doesn't exist (404), try to create a new one
                        if (error.response && error.response.status === 404) {
                            console.log('Conversation not found, attempting to create a new one...');
                            try {
                                // Create a new conversation with the same messages
                                const createResponse = await axios.post('/gaia_chat/api/conversations/create/', {
                                    title: `Conversation ${new Date().toLocaleString()}`,
                                    messages: messages
                                });
                                
                                if (createResponse.data.success) {
                                    console.log('Created new conversation:', createResponse.data.conversation.id);
                                    
                                    // Update the active conversation with the new one
                                    this.activeConversation = createResponse.data.conversation;
                                    
                                    // Update the URL while preserving the current path
                                    const currentPath = window.location.pathname;
                                    const newUrl = `${currentPath}?conversation=${this.activeConversation.id}`;
                                    window.history.pushState({}, '', newUrl);
                                    
                                    return true;
                                }
                            } catch (createError) {
                                console.error('Failed to create new conversation:', createError);
                            }
                        }
                        
                        return false;
                    }
                }
            }
        });
    </script>
</body>
</html>

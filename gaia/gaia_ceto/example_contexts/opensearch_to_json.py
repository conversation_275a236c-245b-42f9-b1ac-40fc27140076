#!/usr/bin/env python3
import json, random
from opensearchpy import OpenSearch, helpers

INDEX = "idx_agsearch_worker_giant1__2025-05-06-000155"
HOSTS = [{"host": "localhost", "port": 9290}]

# ➡️ bump timeout + enable retry on timeout
client = OpenSearch(
    hosts=HOSTS,
    timeout=60,                   # seconds (pick what you need)
    max_retries=3,
    retry_on_timeout=True,
    # http_auth=("user","pass"),  # add if your cluster is secured
)

seed = random.randint(0, 2**31 - 1)
query = {
    "size": 10_000,
    "_source": True,
    "query": {
        "function_score": {
            "random_score": {"seed": seed}
        }
    }
}

resp = client.search(index=INDEX, body=query, request_timeout=60)

with open("agsearch_random_10k.json", "w") as f:
    for hit in resp["hits"]["hits"]:
        json.dump(hit["_source"], f)
        f.write("\n")

print(f"Wrote {len(resp['hits']['hits'])} docs to sample_10k.json")
